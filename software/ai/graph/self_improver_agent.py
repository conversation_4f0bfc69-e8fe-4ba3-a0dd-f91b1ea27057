"""
VeroAgent - Self-improving ReAct Agent using LangGraph

Implementation with:
- LangGraph StateGraph framework
- Iterative prompt optimization
- Score-based evaluation with max attempts mechanism
- Simple and clean approach without hidden prompts
"""

from typing import Dict, Any, List, TypedDict
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import create_react_agent
from software.ai.llm.llm_connect import get_llm_connect
from software.ai.graph.mongodbsaver import VeroMongoDBSaver
from software.ai.graph.director_state import vero_agent
from pydantic import BaseModel, Field


@tool
def python_sandbox(code: str) -> str:
    """Execute Python code in a sandbox environment.

    Args:
        code: Python code to execute

    Returns:
        The output of the code execution
    """
    try:
        namespace = {}
        exec(code, namespace)

        if 'result' in namespace:
            return str(namespace['result'])
        return "Code executed successfully"
    except Exception as e:
        return f"Error: {str(e)}"


class VeroAgent:
    """Self-improving ReAct agent using iterative prompt optimization"""

    class SimpleScore(BaseModel):
        """Simple model for score extraction"""
        score: float = Field(ge=0, le=100, description="Performance score from 0-100")

    class OptimizerState(TypedDict):
        """State for prompt optimization workflow"""
        prompts: List[str]
        task: str
        conversation: List[BaseMessage]
        scores: List[float]
        feedback: str
        eval_parameters: str
        optimized: bool
        score_threshold: int
        max_attempts: int
        attempts: int

    def __init__(self, model, tools=None, checkpointer=None):
        self.model = model
        self.tools = tools
        self.checkpointer = checkpointer
        self._graph = None

    async def tune(self, initial_prompt: str, task: str, eval_parameters: str,
                   config: Dict[str, Any] = None, score_threshold: int = 100, max_attempts: int = 3) -> Dict[str, Any]:
        """Tune the agent's prompt for optimal task performance."""
        if self._graph is None:
            self._graph = self.create_graph()

        initial_state = {
            "prompts": [initial_prompt],
            "task": task,
            "conversation": [],
            "scores": [],
            "feedback": "",
            "optimized": False,
            "score_threshold": score_threshold,
            "max_attempts": max_attempts,
            "attempts": 0,
            "eval_parameters": eval_parameters
        }

        return await self._graph.ainvoke(initial_state, config)
    
    @vero_agent("Running Agent")
    async def run_react_agent(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Run the react agent with current prompt"""
        prompts = state["prompts"]
        current_prompt = prompts[-1]
        task = state["task"]

        print(f"\n[PROMPT] Using prompt: {current_prompt}")
        print(f"[INFO] Attempt {len(prompts)} of {state['max_attempts']}")

        # Create a new agent with the current prompt for each attempt
        def prompt_func(state):
            return [{"role": "system", "content": current_prompt}] + state["messages"]

        agent_executor = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=prompt_func
        )

        # Run the agent with clean messages
        result = await agent_executor.ainvoke({
            "messages": [("user", task)]
        })

        # Add to conversation for evaluation
        conversation = state["conversation"]
        conversation.extend(result["messages"])

        return {"conversation": conversation}

    @vero_agent("Evaluating")
    async def evaluate_score(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Evaluate performance with full context then extract score"""
        conversation = state["conversation"]
        eval_parameters = state["eval_parameters"]
        prompts = state["prompts"]
        current_prompt = prompts[-1]
        task = state["task"]

        # Step 1: Full evaluation with chain
        eval_system = SystemMessage(content=f"""Evaluate AI agent performance against requirements.

Task: {task}
System prompt: {current_prompt}
Requirements: {eval_parameters}

Assessment criteria:
1. Check each requirement
2. Score 0-100 based on compliance
3. Be precise and objective

Focus on requirement fulfillment.""")

        eval_human = HumanMessage(content="Evaluate performance based on the conversation.")

        # Build messages: system + conversation + eval request
        messages_for_eval = [eval_system] + conversation + [eval_human]

        # Get full evaluation
        eval_response = await self.model.ainvoke(messages_for_eval)

        # Add evaluation to conversation
        conversation.append(eval_human)
        conversation.append(eval_response)

        # Step 2: Extract just the score
        score_extraction_prompt = f"Extract the numerical score (0-100) from this evaluation:\n\n{eval_response.content}"
        structured_model = self.model.with_structured_output(self.SimpleScore)
        score_result = await structured_model.ainvoke([HumanMessage(content=score_extraction_prompt)])

        print(f"\n[SCORE] {score_result.score}/100")
        print(f"\n[EVALUATION RESULT]")
        print(eval_response.content)

        scores = state["scores"]
        scores.append(score_result.score)

        attempts = state["attempts"] + 1

        return {
            "conversation": conversation,
            "scores": scores,
            "attempts": attempts
        }

    @vero_agent("Generating Feedback")
    async def generate_feedback(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Generate feedback based on conversation history"""
        scores = state["scores"]
        latest_score = scores[-1]
        score_threshold = state["score_threshold"]

        if latest_score >= score_threshold:
            return {"feedback": "", "optimized": True}

        conversation = state["conversation"]
        eval_parameters = state["eval_parameters"]

        # Analyze score trend
        score_trend = "stable"
        if len(scores) > 1:
            if scores[-1] > scores[-2]:
                score_trend = "improving"
            elif scores[-1] < scores[-2]:
                score_trend = "declining"

        feedback_system = SystemMessage(content=f"""Requirements: {eval_parameters}

Score: {latest_score}/100
Trend for t: {score_trend}

Analyze the performance and suggest one specific improvement to the system prompt.

Focus on addressing the gaps between current performance and requirements.""")

        feedback_human = HumanMessage(content="What specific improvement should be made to the system prompt?")

        messages_for_feedback = [feedback_system] + conversation + [feedback_human]

        feedback_response = await self.model.ainvoke(messages_for_feedback)
        feedback = feedback_response.content

        print(f"\n[FEEDBACK GENERATED]")
        print(feedback)

        return {"feedback": feedback}

    @vero_agent("Optimizing")
    async def optimize_prompt(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Optimize prompt based on full conversation history"""
        feedback = state["feedback"]

        if not feedback:
            return {"optimized": True}

        prompts = state["prompts"]
        current_prompt = prompts[-1]

        optimize_system = SystemMessage(content=f"""Current: {current_prompt}
Improvement: {feedback}

Create an improved system prompt incorporating the suggested change. Keep it concise and clear.""")

        optimize_human = HumanMessage(content="Generate the improved system prompt.")
        optimize_response = await self.model.ainvoke([optimize_system, optimize_human])
        new_prompt = optimize_response.content.strip()

        # Add new prompt
        prompts.append(new_prompt)

        print(f"\n[NEW OPTIMIZED PROMPT]")
        print(new_prompt)

        return {
            "prompts": prompts,
            "optimized": True
        }

    def should_continue(self, state: "VeroAgent.OptimizerState") -> str:
        """Decide whether to continue optimization"""
        scores = state["scores"]
        latest_score = scores[-1]

        score_threshold = state["score_threshold"]
        max_attempts = state["max_attempts"]
        attempts = state["attempts"]

        # Success - reached threshold
        if latest_score >= score_threshold:
            return "end"

        # Max attempts reached
        if attempts >= max_attempts:
            return "end"

        # Early termination if strategy is failing badly
        if len(scores) >= 2:
            # Score dropped by more than 50%
            if scores[-1] < scores[-2] * 0.5:
                print(f"\n[EARLY TERMINATION] Score dropped significantly ({scores[-2]} → {scores[-1]})")
                return "end"

            # Three consecutive declining scores
            if len(scores) >= 3 and scores[-1] < scores[-2] < scores[-3]:
                print(f"\n[EARLY TERMINATION] Three consecutive declining scores")
                return "end"

        return "optimize"

    def create_graph(self) -> StateGraph:
        """Create optimizer graph with 4 nodes"""
        builder = StateGraph(self.OptimizerState)

        builder.add_node("run_agent", self.run_react_agent)
        builder.add_node("evaluate", self.evaluate_score)
        builder.add_node("provide_feedback", self.generate_feedback)
        builder.add_node("optimize", self.optimize_prompt)

        builder.add_edge(START, "run_agent")
        builder.add_edge("run_agent", "evaluate")
        builder.add_edge("evaluate", "provide_feedback")

        builder.add_conditional_edges(
            "provide_feedback",
            self.should_continue,
            {
                "optimize": "optimize",
                "end": END
            }
        )

        builder.add_edge("optimize", "run_agent")

        return builder.compile(checkpointer=self.checkpointer)


async def main():
    """Demonstrate VeroAgent with self-improvement"""
    print("🤖 VeroAgent Demo")
    print("=" * 40)

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()

    agent = VeroAgent(
        model=model,
        tools=[python_sandbox],
        checkpointer=VeroMongoDBSaver()
    )

    result = await agent.tune(
        initial_prompt="You are helpful assistant.",
        task="How many words in this sentence, given we are now in 2025?",
        eval_parameters="Correctness: Must provide exact answer, which is 11 • Must use tools, using XML format • Do not leak the answer in the prompt",
        config={
            "configurable": {"thread_id": "vero-agent-test"},
            "recursion_limit": 50
        },
        score_threshold=100,
        max_attempts=3
    )

    # Get best results
    scores = result['scores']
    prompts = result['prompts']

    best_idx = scores.index(max(scores))
    best_prompt = prompts[best_idx]
    best_score = scores[best_idx]

    print(f"\n[FINAL RESULTS]")
    print(f"Best score: {best_score:.0f}/100")
    print(f"All scores: {', '.join([f'{s:.0f}' for s in scores])}")
    print(f"\n[BEST PROMPT]")
    print(best_prompt)


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
