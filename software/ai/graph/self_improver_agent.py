"""
Simple ReAct Agent with Evaluation Skeleton using LangGraph

Basic implementation with:
- LangGraph StateGraph framework
- State model for workflow
- Simple ReAct agent node
- Basic evaluation node
- No system prompt improvement attempts
"""

from typing import Dict, Any, TypedDict
from langchain_core.messages import HumanMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import create_react_agent
from software.ai.llm.llm_connect import get_llm_connect
from software.ai.graph.mongodbsaver import VeroMongoDBSaver
from software.ai.graph.director_state import vero_agent


@tool
def python_sandbox(code: str) -> str:
    """Execute Python code in a sandbox environment.
    
    Args:
        code: Python code to execute
        
    Returns:
        The output of the code execution
    """
    try:
        namespace = {}
        exec(code, namespace)
        
        if 'result' in namespace:
            return str(namespace['result'])
        else:
            return "Code executed successfully"
    except Exception as e:
        return f"Error: {str(e)}"


class AgentState(TypedDict):
    """State model for the workflow"""
    prompt: str
    question: str
    eval_parameters: str
    agent_response: Dict[str, Any]
    evaluation: Dict[str, Any]
    final_result: str


class SimpleReActAgent:
    """Simple ReAct agent with LangGraph framework"""

    def __init__(self, model, checkpointer=None):
        self.model = model
        self.checkpointer = checkpointer
        self._graph = None

    async def run(self, prompt: str, question: str, eval_parameters: str,
                  config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Run the LangGraph workflow"""

        if self._graph is None:
            self._graph = self.create_graph()

        if config is None:
            config = {
                "configurable": {"thread_id": "simple-react-agent"},
                "recursion_limit": 10
            }

        initial_state = {
            "prompt": prompt,
            "question": question,
            "eval_parameters": eval_parameters,
            "agent_response": {},
            "evaluation": {},
            "final_result": ""
        }

        result = await self._graph.ainvoke(initial_state, config)
        return result
    
    @vero_agent("Running ReAct Agent")
    async def run_react_agent(self, state: AgentState) -> Dict[str, Any]:
        """Node: Run the ReAct agent"""
        prompt = state["prompt"]
        question = state["question"]

        # Create ReAct agent
        agent = create_react_agent(
            model=self.model,
            tools=[python_sandbox],
            state_modifier=prompt
        )

        # Run agent
        result = await agent.ainvoke({
            "messages": [("user", question)]
        })

        return {
            "agent_response": result
        }

    @vero_agent("Evaluating Response")
    async def evaluate_response(self, state: AgentState) -> Dict[str, Any]:
        """Node: Evaluate the agent response"""
        agent_response = state["agent_response"]
        eval_parameters = state["eval_parameters"]

        messages = agent_response.get("messages", [])

        # Extract final answer
        final_answer = self._extract_answer(messages)

        # Simple evaluation
        evaluation_prompt = f"""Evaluate this agent conversation:

EVALUATION CRITERIA:
{eval_parameters}

AGENT CONVERSATION:
{self._format_messages(messages)}

Provide a simple evaluation focusing on whether the criteria were met."""

        eval_response = await self.model.ainvoke([HumanMessage(content=evaluation_prompt)])

        evaluation = {
            "final_answer": final_answer,
            "evaluation_text": eval_response.content,
            "messages_count": len(messages)
        }

        return {
            "evaluation": evaluation,
            "final_result": f"Agent gave answer: {final_answer}\nEvaluation: {eval_response.content[:100]}..."
        }

    def create_graph(self) -> StateGraph:
        """Create the LangGraph workflow"""
        builder = StateGraph(AgentState)

        # Add nodes
        builder.add_node("run_react_agent", self.run_react_agent)
        builder.add_node("evaluate_response", self.evaluate_response)

        # Add edges
        builder.add_edge(START, "run_react_agent")
        builder.add_edge("run_react_agent", "evaluate_response")
        builder.add_edge("evaluate_response", END)

        return builder.compile(checkpointer=self.checkpointer)

    def _extract_answer(self, messages) -> str:
        """Extract answer from conversation"""
        for msg in reversed(messages):
            content = str(getattr(msg, 'content', ''))
            if content and len(content) > 0:
                return content
        return "No answer found"

    def _format_messages(self, messages) -> str:
        """Format messages for evaluation"""
        formatted = []
        for msg in messages:
            msg_type = type(msg).__name__
            content = str(getattr(msg, 'content', ''))
            formatted.append(f"{msg_type}: {content}")
        return "\n".join(formatted)


async def main():
    """Demonstrate simple ReAct agent"""
    print("🤖 Simple ReAct Agent Demo")
    print("=" * 40)
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    agent = SimpleReActAgent(
        model=model,
        checkpointer=VeroMongoDBSaver()
    )
    
    result = await agent.run(
        prompt="You are helpful assistant.",
        question="how many words in this sentence, giving is we are now in 2025?",
        eval_parameters="Correctness: Must provide exact answer, which is 11 • Must use tools, using XML format • Do not leak the answer in the prompt"
    )
    
    print("\n📊 RESULTS")
    print("=" * 40)
    print(f"Final Answer: {result['evaluation']['final_answer'][:100]}...")
    print(f"Messages Count: {result['evaluation']['messages_count']}")
    print(f"\nEvaluation:")
    print(result['evaluation']['evaluation_text'][:200] + "...")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
